import Header from "@/components/header/Header.jsx";
import Footer from "@/components/footer/Footer.jsx";
import Image from "next/image";
import Link from "next/link";
import { Card, CardContent } from "@/components/ui/Card";
import Button from "@/components/ui/Button";
import Badge from "@/components/ui/Badge";

export const metadata = {
  title: "Destinations Tunisie - Bouguerrouche Travel",
  description:
    "Découvrez nos destinations en Tunisie : Tunis, Sousse, Djerba, Hammamet et plus. Réservez votre voyage avec Bouguerrouche Travel.",
  keywords:
    "destinations Tunisie, Tunis, Sousse, Djerba, Hammamet, voyage Tunisie",
};

const destinations = [
  {
    id: 1,
    name: "Tunis",
    region: "Nord de la Tunisie",
    description:
      "La capitale historique de la Tunisie, riche en culture et en histoire. Découvrez la médina classée au patrimoine mondial de l'UNESCO.",
    image: "/images/ExploreTunisia/exploretunisia1.jpg",
    highlights: [
      "Médina de Tunis",
      "Musée du Bardo",
      "Carthage",
      "Sidi Bou Saïd",
    ],
    bestTime: "Mars - Mai, Septembre - Novembre",
    duration: "3-4 jours",
  },
  {
    id: 2,
    name: "Sousse",
    region: "Centre-Est",
    description:
      "Station balnéaire populaire avec des plages magnifiques et une médina historique. Parfait pour allier détente et culture.",
    image: "/images/ExploreTunisia/exploretunisia2.jpg",
    highlights: [
      "Plages de Sousse",
      "Médina de Sousse",
      "Port El Kantaoui",
      "Monastir",
    ],
    bestTime: "Mai - Octobre",
    duration: "5-7 jours",
  },
  {
    id: 3,
    name: "Djerba",
    region: "Sud-Est",
    description:
      "L'île aux mille palmiers, destination paradisiaque avec des plages de sable fin et une culture berbère authentique.",
    image: "/images/ExploreTunisia/exploretunisia3.jpg",
    highlights: [
      "Plages de Djerba",
      "Houmt Souk",
      "Synagogue de la Ghriba",
      "Villages berbères",
    ],
    bestTime: "Avril - Juin, Septembre - Novembre",
    duration: "5-8 jours",
  },
  {
    id: 4,
    name: "Hammamet",
    region: "Nord-Est",
    description:
      "Perle du golfe de Hammamet, réputée pour ses plages, ses spas et son ambiance relaxante. Destination wellness par excellence.",
    image: "/images/back.png",
    highlights: [
      "Plages d'Hammamet",
      "Médina d'Hammamet",
      "Yasmine Hammamet",
      "Centres de thalassothérapie",
    ],
    bestTime: "Mai - Octobre",
    duration: "4-6 jours",
  },
  {
    id: 5,
    name: "Tozeur",
    region: "Sud-Ouest",
    description:
      "Porte du désert du Sahara, oasis spectaculaire avec des palmeraies et des paysages désertiques à couper le souffle.",
    image: "/images/back.png",
    highlights: [
      "Oasis de Tozeur",
      "Chott el-Jérid",
      "Douz",
      "Excursions dans le Sahara",
    ],
    bestTime: "Octobre - Avril",
    duration: "3-5 jours",
  },
  {
    id: 6,
    name: "Kairouan",
    region: "Centre",
    description:
      "Ville sainte de l'Islam, quatrième ville sainte après La Mecque, Médine et Jérusalem. Patrimoine religieux exceptionnel.",
    image: "/images/back.png",
    highlights: [
      "Grande Mosquée",
      "Médina de Kairouan",
      "Bassins des Aghlabides",
      "Artisanat traditionnel",
    ],
    bestTime: "Mars - Mai, Septembre - Novembre",
    duration: "2-3 jours",
  },
];

export default function Destinations() {
  return (
    <>
      <Header />
      <main className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <section className="relative h-[60vh] md:h-[80vh] min-h-[400px] w-full overflow-hidden flex items-center justify-center">
          <Image
            src="/images/landingpagebackground.svg"
            alt="Destinations Tunisia background"
            fill
            className="object-cover z-0"
            priority
            sizes="100vw"
          />
          <div className="relative z-10 text-center text-white px-4">
            <h1 className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-extrabold mb-2 leading-tight tracking-tight font-poppins">
              Destinations en Tunisie
            </h1>
            <hr className="border-t border-white w-full md:w-[90%] mb-6 mt-2 opacity-80 mx-auto" />
            <p className="text-lg sm:text-xl text-gray-200 mb-8 sm:mb-10 leading-relaxed max-w-3xl mx-auto font-poppins">
              Découvrez la beauté et la richesse culturelle de la Tunisie avec
              nos destinations soigneusement sélectionnées
            </p>
          </div>
        </section>

        {/* Destinations Grid */}
        <section className="w-full bg-white section-padding">
          <div className="container-custom">
            <div className="text-center mb-10">
              <h2 className="text-heading text-3xl md:text-4xl text-center mb-10 tracking-tight text-gray-900">
                NOS DESTINATIONS PHARES
              </h2>
              <p className="text-body max-w-2xl mx-auto">
                De la capitale historique aux oasis du désert, explorez la
                diversité exceptionnelle de la Tunisie
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
              {destinations.map((destination) => (
                <Card
                  key={destination.id}
                  className="travel-card-shadow flex flex-col relative"
                  hover={true}
                  padding="none"
                >
                  <div className="relative w-full h-44">
                    <Image
                      src={destination.image}
                      alt={destination.name}
                      fill
                      className="object-cover"
                      sizes="(max-width: 768px) 100vw, 33vw"
                    />
                    <div className="absolute top-3 left-3">
                      <Badge variant="primary" className="shadow-md">
                        {destination.region}
                      </Badge>
                    </div>
                  </div>

                  <CardContent className="flex-1 flex flex-col p-4">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-heading text-lg">
                        {destination.name}
                      </span>
                    </div>
                    <div className="text-caption mb-2">
                      {destination.region}
                    </div>
                    <div className="text-body text-xs mb-3 flex flex-col gap-1">
                      <span>{destination.description}</span>
                    </div>

                    <div className="mb-3">
                      <h4 className="text-subheading text-sm mb-2">
                        Points d&apos;intérêt :
                      </h4>
                      <div className="flex flex-wrap gap-1">
                        {destination.highlights
                          .slice(0, 3)
                          .map((highlight, index) => (
                            <Badge
                              key={index}
                              variant="outline"
                              size="sm"
                              className="bg-accent/10 text-accent-dark border-accent/20"
                            >
                              {highlight}
                            </Badge>
                          ))}
                      </div>
                    </div>

                    <div className="flex justify-between items-center text-caption mb-3">
                      <div>
                        <span className="font-semibold">Période :</span>
                        <br />
                        {destination.bestTime}
                      </div>
                      <div>
                        <span className="font-semibold">Durée :</span>
                        <br />
                        {destination.duration}
                      </div>
                    </div>

                    <div className="flex items-end justify-between mt-auto pt-2">
                      <Link href={`/destinations/${destination.id}`}>
                        <Button variant="primary" size="sm">
                          DÉCOUVRIR
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Call to Action */}
        <section className="w-full bg-white section-padding">
          <div className="container-custom text-center">
            <h2 className="text-heading text-3xl md:text-4xl text-center mb-10 tracking-tight text-gray-900">
              PRÊT À PARTIR À L&apos;AVENTURE ?
            </h2>
            <p className="text-body mb-8 max-w-2xl mx-auto">
              Contactez-nous pour planifier votre voyage sur mesure en Tunisie
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/contact">
                <Button
                  variant="secondary"
                  size="lg"
                  className="rounded-full text-lg"
                >
                  Nous Contacter
                </Button>
              </Link>
              <Link href="/">
                <Button
                  variant="outline"
                  size="lg"
                  className="rounded-full text-lg"
                >
                  Retour à l&apos;accueil
                </Button>
              </Link>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
