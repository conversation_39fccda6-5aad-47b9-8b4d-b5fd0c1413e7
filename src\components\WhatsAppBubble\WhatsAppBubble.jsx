import { FaWhatsapp } from "react-icons/fa";

export default function WhatsAppBubble() {
  const whatsappNumber = "2130562229663";
  const whatsappMessage = "Bonjour! Je souhaite obtenir plus d'informations.";

  const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(
    whatsappMessage
  )}`;

  return (
    <a
      href={whatsappUrl}
      target="_blank"
      rel="noopener noreferrer"
      className="fixed bottom-6 right-6 z-50 bg-green-500 hover:bg-green-600 text-white p-4 rounded-full shadow-lg transition-all duration-300 hover:scale-110 flex items-center justify-center animate-pulse hover:animate-none"
      style={{
        boxShadow: "0 4px 16px rgba(16, 185, 129, 0.3)",
      }}
      aria-label="Contact us on WhatsApp"
    >
      <FaWhatsapp className="w-6 h-6" />
    </a>
  );
}
