import Image from "next/image";

const destinations = [
  {
    title: "Hamamat",
    image: "/images/ExploreTunisia/exploretunisia1.jpg",
  },
  {
    title: "Serene Sanctuary",
    image: "/images/ExploreTunisia/exploretunisia2.jpg",
  },
  {
    title: "Verdant Vista",
    image: "/images/ExploreTunisia/exploretunisia3.jpg",
  },
];

export default function ExploreTunisia() {
  return (
    <section className="relative min-h-screen  flex items-center justify-center text-white ">
      {/* Background Image */}
      <div className="absolute inset-0 z-0 w-full h-full min-h-screen">
        <img
          src="/images/ExploreTunisia/explorebackground.jpg"
          alt="Explore Tunisia"
          className="object-cover absolute inset-0 w-full h-full z-0"
        />
        <div className="absolute inset-0  bg-opacity-40" />
      </div>

      {/* Foreground Content */}
      <div className="relative z-10 max-w-7xl px-4 sm:px-6 lg:px-8 w-full mx-auto flex flex-col md:flex-row items-start md:items-center justify-between gap-8 md:gap-12">
        {/* Left: Text */}
        <div className="max-w-xl w-full text-left flex flex-col justify-center py-8 md:py-0 md:mt-0">
          <h1
            className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-extrabold mb-2 leading-tight tracking-tight"
            style={{ textShadow: "0px 8px 16px rgba(0,0,0,0.5)" }}
          >
            Explore Tunisia
          </h1>
          <hr className="border-t border-white w-full md:w-[90%] mb-6 mt-2 opacity-80" />
          <p className="text-lg sm:text-xl text-gray-200 mb-8 sm:mb-10 leading-relaxed max-w-lg">
            La Tunisie est un pays riche en histoire et culture, célèbre pour
            ses plages, ses sites archéologiques et sa cuisine. Explorez les
            médinas et découvrez l'hospitalité de ses habitants.
          </p>
          <button className="flex items-center gap-2 bg-white text-black px-8 sm:px-10 py-3 sm:py-4 rounded-full font-extrabold text-lg sm:text-xl shadow-2xl hover:bg-gray-100 transition border-2 border-white">
            See all
            <span className="ml-2">
              <svg
                width="24"
                height="24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2.5"
                viewBox="0 0 24 24"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="5" y1="12" x2="19" y2="12" />
                <polyline points="12 5 19 12 12 19" />
              </svg>
            </span>
          </button>
        </div>

        {/* Right: Cards */}
        <div className="flex gap-6 md:gap-8 overflow-x-auto md:overflow-visible pb-4 justify-start md:justify-center items-end md:items-center w-full md:w-auto mt-8 md:mt-0">
          {destinations.map((dest, i) => (
            <div
              key={i}
              className="backdrop-blur-md bg-white/20 border border-white/30 rounded-3xl overflow-hidden shadow-2xl w-[200px] sm:w-[240px] shrink-0 flex flex-col items-center transition-transform hover:scale-105"
            >
              <div className="relative h-40 sm:h-48 w-full">
                <Image
                  src={dest.image}
                  alt={dest.title}
                  fill
                  className="object-cover"
                />
              </div>
              <div className="pt-3 pb-4 text-center text-white font-extrabold text-base sm:text-lg">
                {dest.title}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}