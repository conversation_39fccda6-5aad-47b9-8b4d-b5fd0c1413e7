"use client";
import { FaDollarSign, FaUserShield, FaClock } from "react-icons/fa";
import { Card, CardContent } from "@/components/ui/Card";

export default function Hero() {
  const features = [
    {
      icon: <FaDollarSign className="text-accent text-5xl" strokeWidth={1.5} />,
      title: "Prix Compétitifs",
      description:
        "Profitez des meilleurs tarifs du marché pour tous vos voyages, sans frais cachés.",
    },
    {
      icon: <FaUserShield className="text-accent text-5xl" strokeWidth={1.5} />,
      title: "Réservation Sécurisée",
      description:
        "Vos paiements et données sont protégés grâce à notre système de sécurité avancé.",
    },
    {
      icon: <FaClock className="text-accent text-5xl" strokeWidth={1.5} />,
      title: "Expérience Fluide",
      description:
        "Réservez en quelques clics avec une interface simple, rapide et intuitive.",
    },
  ];

  return (
    <section className="w-full bg-white section-padding">
      <div className="container-custom">
        <h2 className="text-heading text-3xl md:text-4xl text-center mb-10 tracking-tight text-gray-900">
          POURQUOI NOUS CHOISIR ?
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12 justify-items-center">
          {features.map((feature, index) => (
            <Card
              key={index}
              className="flex flex-col items-center text-center max-w-sm"
              hover={true}
            >
              <CardContent className="flex flex-col items-center">
                <div
                  className="bg-accent/10 rounded-xl shadow-sm flex items-center justify-center mb-6 animate-fade-in"
                  style={{ width: 100, height: 100 }}
                >
                  {feature.icon}
                </div>
                <h3 className="text-heading text-lg mb-3">{feature.title}</h3>
                <p className="text-body text-center">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
