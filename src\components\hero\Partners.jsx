"use client";

import Image from "next/image";

export default function Partners() {
  const partners = [
    {
      name: "Air France",
      logo: "/images/partners/airfrance.svg",
    },
    {
      name: "Turkish Airlines",
      logo: "/images/partners/turkish-airlines.svg",
    },
    {
      name: "Emirates",
      logo: "/images/partners/emirates.svg",
    },
    {
      name: "Qatar Airways",
      logo: "/images/partners/qatar-airways.svg",
    },
    {
      name: "<PERSON>riot<PERSON>",
      logo: "/images/partners/marriott.svg",
    },
    {
      name: "<PERSON>",
      logo: "/images/partners/hilton.svg",
    },
  ];

  return (
    <section className="w-full bg-white section-padding">
      <div className="container-custom">
        <h2 className="text-heading text-3xl md:text-4xl text-center mb-10 tracking-tight text-gray-900">
          NOS PARTENAIRES
        </h2>
        <div className="overflow-hidden w-full">
          <div className="flex items-center gap-16 animate-marquee whitespace-nowrap px-6">
            {partners.concat(partners).map((partner, idx) => (
              <div
                key={idx}
                className="flex flex-col items-center min-w-[150px]"
              >
                <div className="relative w-20 h-12 mb-2">
                  <Image
                    src={partner.logo}
                    alt={partner.name}
                    fill
                    className="object-contain"
                  />
                </div>
                <span className="text-subheading text-lg">{partner.name}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
      <style jsx global>{`
        @keyframes marquee {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-50%);
          }
        }
        .animate-marquee {
          animation: marquee 30s linear infinite;
        }
      `}</style>
    </section>
  );
}
