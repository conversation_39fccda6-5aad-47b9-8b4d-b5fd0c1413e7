"use client";
import { useState } from "react";
import {
  FaSearch,
  FaMapMarkerAlt,
  FaCalendarAlt,
  FaUsers,
} from "react-icons/fa";
import { Card } from "@/components/ui/Card";
import Button from "@/components/ui/Button";

export default function BookingSearchBox({ className = "", style = {} }) {
  const [searchData, setSearchData] = useState({
    location: "",
    arrival: "",
    departure: "",
    guests: "",
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSearch = (e) => {
    e.preventDefault();
    console.log("Search data:", searchData);
  };

  return (
    <Card
      className={`relative rounded-3xl travel-card-shadow max-w-4xl w-[95%] md:w-4/5 lg:w-3/5 mx-auto flex flex-col items-start ${className}`}
      style={style}
      padding="lg"
    >
      <h1 className="text-heading text-2xl md:text-4xl text-accent mb-1 md:mb-2">
        Bonjour !
      </h1>
      <p className="text-body text-sm md:text-xl mb-4 md:mb-6">
        Explorez de magnifiques endroits dans le monde avec nous
      </p>
      <div className="w-full border-b border-gray-200 mb-4" />

      <form
        onSubmit={handleSearch}
        className="relative w-full flex flex-col md:flex-row items-stretch gap-4 md:gap-0"
      >
        {/* Location Input */}
        <div className="flex flex-col md:flex-row items-start md:items-center justify-start px-2 md:px-6 py-3 md:py-2 flex-1 min-w-0 border-b md:border-b-0 border-gray-200">
          <span className="flex items-center gap-2 text-gray-500 text-sm font-semibold mb-2 md:mb-0 min-w-[60px]">
            <FaMapMarkerAlt className="text-lg text-accent" />
            <span>Lieu</span>
          </span>
          <input
            type="text"
            name="location"
            placeholder="Ajouter une destination"
            value={searchData.location}
            onChange={handleInputChange}
            className="bg-transparent outline-none text-gray-700 placeholder-gray-400 text-base w-full min-w-0"
            autoComplete="off"
          />
        </div>

        {/* Divider */}
        <div className="hidden md:block w-px bg-gray-200 my-3" />

        {/* Arrival Input */}
        <div className="flex flex-col md:flex-row items-start md:items-center justify-start px-2 md:px-6 py-3 md:py-2 flex-1 min-w-0 border-b md:border-b-0 border-gray-200">
          <span className="flex items-center gap-2 text-gray-500 text-sm font-semibold mb-2 md:mb-0 min-w-[60px]">
            <FaCalendarAlt className="text-lg text-accent" />
            <span>Arrivée</span>
          </span>
          <input
            type="date"
            name="arrival"
            placeholder="Add dates"
            value={searchData.arrival}
            onChange={handleInputChange}
            className="bg-transparent outline-none text-gray-700 placeholder-gray-400 text-base w-full min-w-0"
          />
        </div>

        {/* Divider */}
        <div className="hidden md:block w-px bg-gray-200 my-3" />

        {/* Departure Input */}
        <div className="flex flex-col md:flex-row items-start md:items-center justify-start px-2 md:px-6 py-3 md:py-2 flex-1 min-w-0 border-b md:border-b-0 border-gray-200">
          <span className="flex items-center gap-2 text-gray-500 text-sm font-semibold mb-2 md:mb-0 min-w-[60px]">
            <FaCalendarAlt className="text-lg text-accent" />
            <span>Départ</span>
          </span>
          <input
            type="date"
            name="departure"
            placeholder="Add dates"
            value={searchData.departure}
            onChange={handleInputChange}
            className="bg-transparent outline-none text-gray-700 placeholder-gray-400 text-base w-full min-w-0"
          />
        </div>

        {/* Divider */}
        <div className="hidden md:block w-px bg-gray-200 my-3" />

        {/* Guests Input */}
        <div className="flex flex-col md:flex-row items-start md:items-center justify-start px-2 md:px-6 py-3 md:py-2 flex-1 min-w-0">
          <span className="flex items-center gap-2 text-gray-500 text-sm font-semibold mb-2 md:mb-0 min-w-[60px]">
            <FaUsers className="text-lg text-accent" />
            <span>Invités</span>
          </span>
          <input
            type="number"
            name="guests"
            min="1"
            placeholder="Add guests"
            value={searchData.guests}
            onChange={handleInputChange}
            className="bg-transparent outline-none text-gray-700 placeholder-gray-400 text-base w-full min-w-0"
          />
        </div>

        {/* Search Button */}
        <div className="flex justify-end mt-4 md:mt-0">
          <Button
            type="submit"
            variant="secondary"
            className="p-4 rounded-full w-12 h-12 flex items-center justify-center"
            aria-label="Rechercher"
          >
            <FaSearch className="w-5 h-5" />
          </Button>
        </div>
      </form>
    </Card>
  );
}
