"use client";
import { forwardRef } from "react";
import { cn } from "@/lib/utils";

const Badge = forwardRef(
  (
    {
      className,
      variant = "primary",
      size = "default",
      children,
      ...props
    },
    ref
  ) => {
    const baseClasses = "badge";
    
    const variants = {
      primary: "badge-primary",
      secondary: "badge-secondary",
      success: "badge-success",
      warning: "badge-warning",
      error: "badge-error",
      outline: "badge-outline",
    };

    const sizes = {
      sm: "text-xs px-2 py-0.5",
      default: "text-xs px-3 py-1",
      lg: "text-sm px-4 py-1.5",
    };

    const variantClass = variants[variant] || variants.primary;
    const sizeClass = sizes[size] || sizes.default;

    return (
      <span
        className={cn(
          baseClasses,
          variantClass,
          sizeClass,
          className
        )}
        ref={ref}
        {...props}
      >
        {children}
      </span>
    );
  }
);

Badge.displayName = "Badge";

export default Badge;
